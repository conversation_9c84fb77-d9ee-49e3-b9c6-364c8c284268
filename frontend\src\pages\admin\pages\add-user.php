<?php
// Add User Page
?>

<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-xl font-semibold text-gray-900">Tambah User Baru</h1>
                    <p class="text-sm text-gray-600 mt-1">Buat akun pengguna baru dengan role dan JWT token</p>
                </div>
                <a href="?page=users" class="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Kembali
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- User Form Card -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Informasi Pengguna</h2>
                </div>
                <div class="p-6">
                    <form id="addUserForm">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Name -->
                            <div>
                                <label for="userName" class="block text-sm font-medium text-gray-700 mb-2">Nama Lengkap *</label>
                                <input type="text" id="userName" name="name" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="Masukkan nama lengkap">
                            </div>

                            <!-- Email -->
                            <div>
                                <label for="userEmail" class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                                <input type="email" id="userEmail" name="email" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="Masukkan email">
                            </div>

                            <!-- Password -->
                            <div>
                                <label for="userPassword" class="block text-sm font-medium text-gray-700 mb-2">Password *</label>
                                <input type="password" id="userPassword" name="password" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="Masukkan password (min. 6 karakter)">
                            </div>

                            <!-- Confirm Password -->
                            <div>
                                <label for="userConfirmPassword" class="block text-sm font-medium text-gray-700 mb-2">Konfirmasi Password *</label>
                                <input type="password" id="userConfirmPassword" name="confirm_password" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="Konfirmasi password">
                            </div>

                            <!-- Role -->
                            <div>
                                <label for="userRole" class="block text-sm font-medium text-gray-700 mb-2">Role *</label>
                                <select id="userRole" name="role" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">Pilih Role</option>
                                    <option value="user">User</option>
                                    <option value="admin">Admin</option>
                                    <option value="moderator">Moderator</option>
                                </select>
                            </div>

                            <!-- Status -->
                            <div>
                                <label for="userStatus" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <select id="userStatus" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="active">Aktif</option>
                                    <option value="inactive">Tidak Aktif</option>
                                    <option value="suspended">Suspended</option>
                                </select>
                            </div>
                        </div>

                        <!-- JWT Token Option -->
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <h3 class="text-sm font-medium text-gray-900 mb-4">JWT Token Settings</h3>
                            <div class="flex items-center">
                                <input type="checkbox" id="generateJWT" name="generate_jwt" checked
                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <label for="generateJWT" class="ml-2 text-sm text-gray-700">
                                    Generate JWT Token otomatis setelah user dibuat
                                </label>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">
                                Token akan berlaku selama 24 jam dan dapat digunakan untuk autentikasi API
                            </p>
                        </div>

                        <!-- Action Buttons -->
                        <div class="mt-6 flex justify-end space-x-3">
                            <button type="button" onclick="resetForm()" class="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                                <i class="fas fa-undo mr-2"></i>Reset
                            </button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-plus mr-2"></i>Tambah User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Info Card -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Informasi</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-4 text-sm">
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">Role Pengguna:</h4>
                            <ul class="space-y-1 text-gray-600">
                                <li><strong>User:</strong> Akses terbatas, hanya bisa melihat konten</li>
                                <li><strong>Admin:</strong> Akses penuh ke semua fitur</li>
                                <li><strong>Moderator:</strong> Dapat mengelola konten</li>
                            </ul>
                        </div>
                        
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">Status Akun:</h4>
                            <ul class="space-y-1 text-gray-600">
                                <li><strong>Aktif:</strong> Dapat login dan menggunakan sistem</li>
                                <li><strong>Tidak Aktif:</strong> Akun dinonaktifkan sementara</li>
                                <li><strong>Suspended:</strong> Akun ditangguhkan</li>
                            </ul>
                        </div>

                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">JWT Token:</h4>
                            <p class="text-gray-600">Token akan dibuat otomatis jika dicentang. Token ini digunakan untuk autentikasi API dan berlaku selama 24 jam.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Stats Card -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Statistik User</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-3 text-sm">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-500">Total User</span>
                            <span class="text-gray-900 font-medium" id="totalUsers">-</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-500">User Aktif</span>
                            <span class="text-gray-900 font-medium" id="activeUsers">-</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-500">Admin</span>
                            <span class="text-gray-900 font-medium" id="adminUsers">-</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-500">Dengan JWT Token</span>
                            <span class="text-gray-900 font-medium" id="tokenUsers">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Load user statistics
function loadUserStats() {
    // Try multiple possible paths
    const possiblePaths = [
        '../api.php?action=get_user_stats',
        'api.php?action=get_user_stats',
        '/react-news/frontend/src/pages/admin/api.php?action=get_user_stats',
        'http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_user_stats'
    ];

    const apiUrl = possiblePaths[0]; // Start with relative path
    console.log('🔍 Loading stats from:', apiUrl);
    console.log('📍 Current location:', window.location.href);

    fetch(apiUrl)
        .then(response => {
            console.log('📡 Response status:', response.status);
            console.log('📡 Response URL:', response.url);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('📊 Stats data:', data);
            if (data.success) {
                document.getElementById('totalUsers').textContent = data.stats.total || 0;
                document.getElementById('activeUsers').textContent = data.stats.active || 0;
                document.getElementById('adminUsers').textContent = data.stats.admin || 0;
                document.getElementById('tokenUsers').textContent = data.stats.with_token || 0;
            }
        })
        .catch(error => {
            console.error('❌ Error loading stats:', error);
            console.error('❌ Error details:', error.message);

            // Try alternative path if first one fails
            if (apiUrl === possiblePaths[0]) {
                console.log('🔄 Trying alternative path...');
                tryAlternativePath();
            }
        });
}

// Try alternative API paths
function tryAlternativePath() {
    const alternativeUrl = '/react-news/frontend/src/pages/admin/api.php?action=get_user_stats';
    console.log('🔍 Trying alternative URL:', alternativeUrl);

    fetch(alternativeUrl)
        .then(response => {
            console.log('📡 Alternative response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('📊 Alternative stats data:', data);
            if (data.success) {
                document.getElementById('totalUsers').textContent = data.stats.total || 0;
                document.getElementById('activeUsers').textContent = data.stats.active || 0;
                document.getElementById('adminUsers').textContent = data.stats.admin || 0;
                document.getElementById('tokenUsers').textContent = data.stats.with_token || 0;
            }
        })
        .catch(error => {
            console.error('❌ Alternative path also failed:', error);
            // Set default values
            document.getElementById('totalUsers').textContent = '0';
            document.getElementById('activeUsers').textContent = '0';
            document.getElementById('adminUsers').textContent = '0';
            document.getElementById('tokenUsers').textContent = '0';
        });
}

// Form submission
document.getElementById('addUserForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const password = formData.get('password');
    const confirmPassword = formData.get('confirm_password');
    
    // Validation
    if (password !== confirmPassword) {
        showNotification('Password dan konfirmasi password tidak cocok', 'error');
        return;
    }
    
    if (password.length < 6) {
        showNotification('Password minimal 6 karakter', 'error');
        return;
    }
    
    // Prepare data
    const userData = {
        action: 'add_user',
        name: formData.get('name'),
        email: formData.get('email'),
        password: password,
        role: formData.get('role'),
        status: formData.get('status'),
        generate_jwt: formData.get('generate_jwt') ? '1' : '0'
    };
    
    // Show loading
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Menambahkan...';
    submitBtn.disabled = true;
    
    // Send request
    fetch('../api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams(userData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'User berhasil ditambahkan', 'success');
            
            // Show success with redirect option
            Swal.fire({
                title: 'User Berhasil Ditambahkan!',
                text: data.message,
                icon: 'success',
                showCancelButton: true,
                confirmButtonText: 'Lihat Daftar User',
                cancelButtonText: 'Tambah User Lagi',
                confirmButtonColor: '#3b82f6',
                cancelButtonColor: '#10b981'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = '?page=users';
                } else {
                    // Reset form for new user
                    resetForm();
                    loadUserStats(); // Refresh stats
                }
            });
        } else {
            showNotification(data.message || 'Gagal menambahkan user', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Terjadi kesalahan saat menambahkan user', 'error');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// Reset form
function resetForm() {
    document.getElementById('addUserForm').reset();
    document.getElementById('generateJWT').checked = true; // Default checked
}

// Load stats on page load
document.addEventListener('DOMContentLoaded', function() {
    loadUserStats();
});
</script>
